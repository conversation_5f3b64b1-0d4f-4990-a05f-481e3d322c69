
// Ant Colony Optimization Simulation Core
export const ANT_STATE_SEARCHING = 'searching';
export const ANT_STATE_RETURNING = 'returning';

export type AntState = typeof ANT_STATE_SEARCHING | typeof ANT_STATE_RETURNING;

export interface Ant {
  id: number;
  x: number;
  y: number;
  angle: number; // radians
  state: AntState;
}

export interface Cell {
  foodPheromone: number;
  homePheromone: number;
  isObstacle: boolean;
}

export interface Point {
  x: number;
  y: number;
}

export interface SimulationParams {
  antCount: number;
  evaporationRate: number;
  pheromoneStrength: number;
  trailStrength: number; // Influence of pheromone on turning
  antSpeed: number;
  sensorDistance: number;
  sensorAngle: number; // radians
  turnSpeed: number;
}

export class AntColonySimulation {
  public ants: Ant[] = [];
  public grid: Cell[][] = [];
  public width: number;
  public height: number;
  public nestPosition: Point | null = null;
  public foodSources: Point[] = [];
  
  private params: SimulationParams;

  constructor(width: number, height: number, params: SimulationParams) {
    this.width = Math.floor(width);
    this.height = Math.floor(height);
    this.params = params;
    this.initializeGrid();
    this.initializeAnts();
  }

  private initializeGrid() {
    this.grid = Array.from({ length: this.height }, () =>
      Array.from({ length: this.width }, () => ({
        foodPheromone: 0,
        homePheromone: 0,
        isObstacle: false,
      }))
    );
  }

  private initializeAnts() {
    if (!this.nestPosition) return;
    this.ants = [];
    for (let i = 0; i < this.params.antCount; i++) {
      this.spawnAnt(i);
    }
  }

  public setParams(params: SimulationParams) {
    const oldAntCount = this.params.antCount;
    this.params = params;
    if (this.params.antCount > oldAntCount) {
      for (let i = oldAntCount; i < this.params.antCount; i++) {
        this.spawnAnt(i);
      }
    } else if (this.params.antCount < oldAntCount) {
      this.ants.length = this.params.antCount;
    }
  }

  private spawnAnt(id: number) {
    if (!this.nestPosition) return;
    // 在巢穴周围随机生成蚂蚁位置
    const spawnRadius = 5;
    const angle = Math.random() * 2 * Math.PI;
    const distance = Math.random() * spawnRadius;
    
    this.ants.push({
      id,
      x: Math.max(5, Math.min(this.width - 5, this.nestPosition.x + distance * Math.cos(angle))),
      y: Math.max(5, Math.min(this.height - 5, this.nestPosition.y + distance * Math.sin(angle))),
      angle: Math.random() * 2 * Math.PI,
      state: ANT_STATE_SEARCHING,
    });
  }

  public update() {
    this.updateAnts();
    this.updatePheromones();
  }

  private updateAnts() {
    for (const ant of this.ants) {
      this.moveAnt(ant);
      this.interactWithEnvironment(ant);
      this.depositPheromone(ant);
    }
  }

  private moveAnt(ant: Ant) {
    const pheromoneType = ant.state === ANT_STATE_SEARCHING ? 'homePheromone' : 'foodPheromone';
    
    const sense = (angleOffset: number) => {
      const angle = ant.angle + angleOffset;
      const x = Math.floor(ant.x + this.params.sensorDistance * Math.cos(angle));
      const y = Math.floor(ant.y + this.params.sensorDistance * Math.sin(angle));
      if (this.isOutOfBounds(x, y) || this.grid[y][x].isObstacle) {
        return -1; // Wall detection
      }
      return this.grid[y][x][pheromoneType];
    };

    const forwardSense = sense(0);
    const leftSense = sense(-this.params.sensorAngle);
    const rightSense = sense(this.params.sensorAngle);

    // 更智能的转向逻辑
    if (forwardSense === -1) { // 直接撞墙
      ant.angle += (Math.random() - 0.5) * Math.PI;
    } else if (leftSense === -1 && rightSense === -1) { // 左右都是墙
      // 继续前进或微调
      ant.angle += (Math.random() - 0.5) * this.params.turnSpeed * 0.5;
    } else if (leftSense === -1) { // 左边是墙
      ant.angle += this.params.turnSpeed;
    } else if (rightSense === -1) { // 右边是墙
      ant.angle -= this.params.turnSpeed;
    } else {
      // 根据信息素浓度决定转向
      if (leftSense > forwardSense && leftSense > rightSense) {
        ant.angle -= this.params.turnSpeed;
      } else if (rightSense > forwardSense && rightSense > leftSense) {
        ant.angle += this.params.turnSpeed;
      } else {
        // 添加轻微随机性保持探索性
        ant.angle += (Math.random() - 0.5) * this.params.turnSpeed * 0.3;
      }
    }
    
    // 移动蚂蚁
    const newX = ant.x + this.params.antSpeed * Math.cos(ant.angle);
    const newY = ant.y + this.params.antSpeed * Math.sin(ant.angle);

    // 严格的边界约束
    if (newX <= 2 || newX >= this.width - 2) {
      ant.angle = Math.PI - ant.angle; // 水平反弹
      ant.x = Math.max(2, Math.min(this.width - 2, ant.x));
    } else {
      ant.x = newX;
    }

    if (newY <= 2 || newY >= this.height - 2) {
      ant.angle = -ant.angle; // 垂直反弹
      ant.y = Math.max(2, Math.min(this.height - 2, ant.y));
    } else {
      ant.y = newY;
    }
  }

  private interactWithEnvironment(ant: Ant) {
    // 检查食物
    if (ant.state === ANT_STATE_SEARCHING) {
      for (const food of this.foodSources) {
        const dx = ant.x - food.x;
        const dy = ant.y - food.y;
        if (dx * dx + dy * dy < 8 * 8) { // 8px半径
          ant.state = ANT_STATE_RETURNING;
          ant.angle += Math.PI + (Math.random() - 0.5) * 0.5; // 转身并添加小幅随机
          return;
        }
      }
    }

    // 检查巢穴
    if (ant.state === ANT_STATE_RETURNING && this.nestPosition) {
      const dx = ant.x - this.nestPosition.x;
      const dy = ant.y - this.nestPosition.y;
      if (dx * dx + dy * dy < 8 * 8) { // 8px半径
        ant.state = ANT_STATE_SEARCHING;
        ant.angle += Math.PI + (Math.random() - 0.5) * 0.5; // 转身并添加小幅随机
        return;
      }
    }
  }
  
  private depositPheromone(ant: Ant) {
    const gridX = Math.floor(ant.x);
    const gridY = Math.floor(ant.y);

    if (!this.isOutOfBounds(gridX, gridY)) {
      // 增加信息素强度，让轨迹更明显
      const strength = this.params.pheromoneStrength / 50; // 调整强度
      if (ant.state === ANT_STATE_SEARCHING) {
        this.grid[gridY][gridX].foodPheromone = Math.min(1, this.grid[gridY][gridX].foodPheromone + strength);
      } else { // RETURNING
        this.grid[gridY][gridX].homePheromone = Math.min(1, this.grid[gridY][gridX].homePheromone + strength);
      }
    }
  }
  
  private updatePheromones() {
    // Create a new grid to write updated values to
    const newGrid = this.grid.map(row => row.map(cell => ({...cell})));

    // Diffusion for inner cells
    // This process averages pheromones with neighbors, creating smoother trails.
    // We only do this for inner cells to avoid complex edge-handling.
    for (let y = 1; y < this.height - 1; y++) {
      for (let x = 1; x < this.width - 1; x++) {
        let foodSum = 0;
        let homeSum = 0;
        for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
                foodSum += this.grid[y+dy][x+dx].foodPheromone;
                homeSum += this.grid[y+dy][x+dx].homePheromone;
            }
        }
        
        // The cell's new value is the average of its 3x3 neighborhood.
        newGrid[y][x].foodPheromone = foodSum / 9;
        newGrid[y][x].homePheromone = homeSum / 9;
      }
    }

    // Evaporation for all cells
    // This is applied to the diffused grid. It causes trails to fade over time.
    for (let y = 0; y < this.height; y++) {
        for (let x = 0; x < this.width; x++) {
            newGrid[y][x].foodPheromone *= this.params.evaporationRate;
            newGrid[y][x].homePheromone *= this.params.evaporationRate;
        }
    }
    
    this.grid = newGrid;
  }

  public setObstacle(x: number, y: number, isObstacle: boolean) {
    if (!this.isOutOfBounds(x, y)) {
      this.grid[y][x].isObstacle = isObstacle;
    }
  }

  private isOutOfBounds(x: number, y: number): boolean {
    return x < 0 || x >= this.width || y < 0 || y >= this.height;
  }
  
  public reset() {
    this.initializeGrid();
    this.initializeAnts();
  }
}
