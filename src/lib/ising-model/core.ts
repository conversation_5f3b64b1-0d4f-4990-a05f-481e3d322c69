export interface IsingParams {
  gridSize: number;
  temperature: number; // T
  externalField: number; // H
  thermalWeight: number; // 热运动影响权重
  enableThermalMotion: boolean; // 是否启用热运动
}

export type Spin = 1 | -1;
export type SpinGrid = Spin[][];

export const COUPLING_CONSTANT = 1.0; // J, 耦合常数

export const initialParams: IsingParams = {
  gridSize: 50,
  temperature: 2.5,
  externalField: 0.0,
  thermalWeight: 1.0,
  enableThermalMotion: true,
};

export function createIsingGrid(size: number, initialState: 'Random' | 'Ordered'): SpinGrid {
  const grid: SpinGrid = [];
  for (let y = 0; y < size; y++) {
    grid[y] = [];
    for (let x = 0; x < size; x++) {
      const spin: Spin = initialState === 'Ordered' ? 1 : (Math.random() < 0.5 ? 1 : -1);
      grid[y][x] = spin;
    }
  }
  return grid;
}

export function calculateTotalEnergy(grid: SpinGrid, params: IsingParams): number {
  // Add safety checks
  if (!grid || grid.length === 0) {
    console.log('Grid is empty or undefined');
    return 0;
  }
  
  let energy = 0;
  const { gridSize, externalField } = params;
  const actualGridSize = grid.length;
  
  // Use the actual grid size instead of the parameter if they don't match
  const effectiveGridSize = Math.min(gridSize, actualGridSize);
  
  for (let y = 0; y < effectiveGridSize; y++) {
    // Check if the row exists
    if (!grid[y]) {
      console.log(`Grid row ${y} is undefined`);
      continue;
    }
    
    for (let x = 0; x < effectiveGridSize; x++) {
      // Check if the cell exists
      if (grid[y][x] === undefined) {
        console.log(`Grid cell [${y}][${x}] is undefined`);
        continue;
      }
      
      const spin = grid[y][x];
      
      // Calculate neighbors with safety checks
      const rightNeighbor = grid[y][(x + 1) % effectiveGridSize] || 0;
      const leftNeighbor = grid[y][(x - 1 + effectiveGridSize) % effectiveGridSize] || 0;
      const bottomNeighbor = grid[(y + 1) % effectiveGridSize] ? grid[(y + 1) % effectiveGridSize][x] || 0 : 0;
      const topNeighbor = grid[(y - 1 + effectiveGridSize) % effectiveGridSize] ? grid[(y - 1 + effectiveGridSize) % effectiveGridSize][x] || 0 : 0;
      
      const sumOfNeighbors = rightNeighbor + leftNeighbor + bottomNeighbor + topNeighbor;
      
      // 每个相互作用对只计算一次，所以除以2
      energy -= (COUPLING_CONSTANT * spin * sumOfNeighbors) / 2;
      energy -= externalField * spin;
    }
  }
  return energy;
}

export function calculateTotalMagnetization(grid: SpinGrid): number {
  // Add safety check
  if (!grid || grid.length === 0) {
    return 0;
  }
  
  let magnetization = 0;
  const size = grid.length;
  for (let y = 0; y < size; y++) {
    if (!grid[y]) continue;
    for (let x = 0; x < grid[y].length; x++) {
      if (grid[y][x] !== undefined) {
        magnetization += grid[y][x];
      }
    }
  }
  return magnetization;
}

// 执行一个蒙特卡洛步 (N*N 次自旋翻转尝试)
export function metropolisStep(grid: SpinGrid, params: IsingParams, currentEnergy: number, currentMagnetization: number): { newGrid: SpinGrid; newEnergy: number; newMagnetization: number } {
  // Add safety check
  if (!grid || grid.length === 0) {
    return { newGrid: grid, newEnergy: currentEnergy, newMagnetization: currentMagnetization };
  }
  
  const { gridSize, temperature, externalField, thermalWeight, enableThermalMotion } = params;
  let newEnergy = currentEnergy;
  let newMagnetization = currentMagnetization;
  const newGrid = grid.map(row => [...row]); // 复制一份以进行修改
  
  const actualGridSize = Math.min(gridSize, grid.length);

  for (let i = 0; i < actualGridSize * actualGridSize; i++) {
    const x = Math.floor(Math.random() * actualGridSize);
    const y = Math.floor(Math.random() * actualGridSize);
    
    // Check if the cell exists
    if (!newGrid[y] || newGrid[y][x] === undefined) {
      continue;
    }
    
    const spin = newGrid[y][x];

    // Calculate neighbors with safety checks
    const rightNeighbor = newGrid[y][(x + 1) % actualGridSize] || 0;
    const leftNeighbor = newGrid[y][(x - 1 + actualGridSize) % actualGridSize] || 0;
    const bottomNeighbor = newGrid[(y + 1) % actualGridSize] ? newGrid[(y + 1) % actualGridSize][x] || 0 : 0;
    const topNeighbor = newGrid[(y - 1 + actualGridSize) % actualGridSize] ? newGrid[(y - 1 + actualGridSize) % actualGridSize][x] || 0 : 0;
    
    const sumOfNeighbors = rightNeighbor + leftNeighbor + bottomNeighbor + topNeighbor;

    const deltaE = 2 * spin * (COUPLING_CONSTANT * sumOfNeighbors + externalField);

    // 根据Metropolis算法决定是否接受翻转
    let acceptFlip = false;
    
    if (!enableThermalMotion) {
      // 不考虑热运动时，只接受降低能量的翻转
      acceptFlip = deltaE < 0;
    } else {
      // 考虑热运动时，使用调节后的温度参数
      const effectiveTemperature = temperature * thermalWeight;
      acceptFlip = deltaE < 0 || (effectiveTemperature > 0.01 && Math.random() < Math.exp(-deltaE / effectiveTemperature));
    }

    if (acceptFlip) {
      newGrid[y][x] *= -1;
      newEnergy += deltaE;
      newMagnetization -= 2 * spin;
    }
  }

  return { newGrid, newEnergy, newMagnetization };
}

// 新增：计算磁化率所需的统计量
export interface MagnetizationStats {
  M: number;      // 瞬时磁化强度
  M2: number;     // 瞬时磁化强度的平方
  absM: number;   // 磁化强度绝对值
}

export function calculateMagnetizationStats(grid: SpinGrid): MagnetizationStats {
  const M = calculateTotalMagnetization(grid);
  return {
    M,
    M2: M * M,
    absM: Math.abs(M)
  };
}

// 检查系统是否达到热平衡
function hasReachedEquilibrium(
  history: number[],
  windowSize: number = 50  // 减小窗口大小以更快响应
): boolean {
  if (history.length < windowSize * 2) return false;
  
  const recentWindow = history.slice(-windowSize);
  const previousWindow = history.slice(-2 * windowSize, -windowSize);
  
  const recentMean = recentWindow.reduce((a, b) => a + b) / windowSize;
  const recentVar = recentWindow.reduce((a, b) => a + (b - recentMean) ** 2, 0) / windowSize;
  
  const previousMean = previousWindow.reduce((a, b) => a + b) / windowSize;
  const previousVar = previousWindow.reduce((a, b) => a + (b - previousMean) ** 2, 0) / windowSize;
  
  // 检查均值和方差的相对变化
  const meanChange = Math.abs(recentMean - previousMean) / (Math.abs(previousMean) + 1e-6);
  const varChange = Math.abs(recentVar - previousVar) / (Math.abs(previousVar) + 1e-6);
  
  return meanChange < 0.1 && varChange < 0.1;
}

// 计算磁化率
export function calculateSusceptibility(
  magnetizationHistory: number[],
  temperature: number,
  gridSize: number,
  equilibrationSteps: number = 100  // 减少热化步数要求以更快响应
): number {
  // 确保有足够的数据点进行统计
  if (magnetizationHistory.length < equilibrationSteps + 50) {
    return 0;
  }

  const N = gridSize * gridSize;
  const T = temperature;
  const BOLTZMANN_CONSTANT = 1.0;

  // 使用最近的数据计算，但确保有足够的统计样本
  const sampleSize = Math.min(300, magnetizationHistory.length - equilibrationSteps);
  const recentData = magnetizationHistory.slice(-sampleSize);

  // 计算归一化的磁矩 - 这里是关键，必须保持原始符号！
  const normalizedData = recentData.map(m => m / N);

  // 计算统计量
  const averageM = normalizedData.reduce((sum, m) => sum + m, 0) / normalizedData.length;
  const averageM2 = normalizedData.reduce((sum, m) => sum + m * m, 0) / normalizedData.length;

  // 计算磁化率：χ = N * (⟨M²⟩ - ⟨M⟩²) / (kT)
  // 注意：这里使用原始磁化强度的方差，然后乘以N来得到正确的磁化率
  const magnetizationVariance = averageM2 - averageM * averageM;
  const susceptibility = N * magnetizationVariance / (BOLTZMANN_CONSTANT * T);

  // 在临界点附近，磁化率应该显著增大，不应该被限制为0
  return Math.max(0, susceptibility);
}
