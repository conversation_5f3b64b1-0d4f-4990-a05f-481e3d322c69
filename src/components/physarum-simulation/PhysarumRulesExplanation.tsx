
import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

const AntColonyRulesExplanation: React.FC = () => {
  return (
    <div className="absolute bottom-4 left-4 max-w-sm w-full bg-background/80 p-3 rounded-lg text-xs backdrop-blur-sm">
      <Accordion type="single" collapsible>
        <AccordionItem value="item-1">
          <AccordionTrigger className="text-base font-semibold text-green-300">
            蚁群觅食 - 路径的智慧
          </AccordionTrigger>
          <AccordionContent className="text-muted-foreground space-y-2">
            <p>
              本模拟展示了群体智能中的“施蒂格默”现象：个体通过改变环境来进行间接通信。大量简单的“蚂蚁”遵循几条基本规则，却能涌现出寻找最优路径的复杂集体行为。
            </p>
            <h4 className="font-bold text-foreground">核心规则:</h4>
            <ul className="list-disc pl-4 space-y-1">
              <li><strong className="text-green-400">寻找食物 (Searching):</strong> 蚂蚁留下“觅食信息素”(绿色)，并被“返程信息素”(蓝色)吸引，以找到回家的路。</li>
              <li><strong className="text-blue-400">返回巢穴 (Returning):</strong> 找到食物后，蚂蚁留下“返程信息素”，并被“觅食信息素”吸引，以沿着已建立的路径返回。</li>
              <li><strong className="text-foreground">信息素蒸发:</strong> 所有信息素都会随时间缓慢蒸发和扩散。这使得更短、更常用的路径信息素浓度更高，从而被强化；而长路径则被“遗忘”。</li>
            </ul>
            <p>
              观察蚁群如何在您设置的障碍物周围，自适应地建立起最高效的觅食通道。
            </p>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default AntColonyRulesExplanation;
