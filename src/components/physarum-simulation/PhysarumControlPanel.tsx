
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Play, Pause, RefreshCw, MousePointer, Eraser, Move, Bot, Apple } from 'lucide-react';
import { SimulationParams } from '@/lib/physarum/core';

export type Tool = 'pointer' | 'draw' | 'erase' | 'move_nest' | 'move_food';

interface ControlPanelProps {
  params: SimulationParams;
  setParams: (params: SimulationParams) => void;
  isRunning: boolean;
  setIsRunning: (running: boolean) => void;
  resetSimulation: () => void;
  activeTool: Tool;
  setActiveTool: (tool: Tool) => void;
}

const AntColonyControlPanel: React.FC<ControlPanelProps> = ({ 
  params, 
  setParams, 
  isRunning, 
  setIsRunning, 
  resetSimulation,
  activeTool,
  setActiveTool
}) => {

  const handleParamChange = (key: keyof SimulationParams, value: any) => {
    setParams({ ...params, [key]: value });
  };

  const getToolDescription = (tool: Tool) => {
    switch(tool) {
      case 'pointer': return '默认指针 - 观察模式';
      case 'draw': return '绘制障碍物 - 点击拖拽添加墙壁';
      case 'erase': return '擦除障碍物 - 点击拖拽移除墙壁';
      case 'move_nest': return '移动巢穴 - 点击拖拽改变蚂蚁家园位置';
      case 'move_food': return '移动食物 - 点击拖拽改变食物源位置';
      default: return '';
    }
  };

  return (
    <div className="w-full md:w-80 lg:w-96 p-4 bg-background border-l overflow-y-auto flex flex-col gap-4 text-sm">
      <h2 className="text-xl font-bold text-green-400">蚁群觅食控制中心</h2>
      
      <div className="grid grid-cols-2 gap-2">
        <Button onClick={() => setIsRunning(!isRunning)} variant="outline" size="sm" className="border-green-500 text-green-400 hover:bg-green-500/20">
          {isRunning ? <><Pause /><span>暂停</span></> : <><Play /><span>开始</span></>}
        </Button>
        <Button onClick={resetSimulation} variant="destructive" size="sm" className="bg-red-600 hover:bg-red-700">
          <RefreshCw />
          <span>重置</span>
        </Button>
      </div>

      <div className="space-y-3">
        <Label className="text-green-200 font-semibold">交互工具</Label>
        <ToggleGroup 
          type="single" 
          value={activeTool} 
          onValueChange={(value: Tool) => value && setActiveTool(value)} 
          className="grid grid-cols-2 gap-2 mt-2"
        >
          <ToggleGroupItem value="pointer" aria-label="Pointer" className="flex flex-col items-center gap-1 h-16">
            <MousePointer size={16} />
            <span className="text-xs">观察</span>
          </ToggleGroupItem>
          <ToggleGroupItem value="draw" aria-label="Draw Obstacle" className="flex flex-col items-center gap-1 h-16">
            <Bot size={16} color="#8f8f8f" />
            <span className="text-xs">绘制墙</span>
          </ToggleGroupItem>
          <ToggleGroupItem value="erase" aria-label="Erase Obstacle" className="flex flex-col items-center gap-1 h-16">
            <Eraser size={16} />
            <span className="text-xs">擦除墙</span>
          </ToggleGroupItem>
          <ToggleGroupItem value="move_nest" aria-label="Move Nest" className="flex flex-col items-center gap-1 h-16">
            <div className="flex items-center">
              <Move size={12} />
              <div className="w-3 h-3 bg-orange-400 rounded-full ml-1"></div>
            </div>
            <span className="text-xs">移动巢</span>
          </ToggleGroupItem>
          <ToggleGroupItem value="move_food" aria-label="Move Food" className="flex flex-col items-center gap-1 h-16">
            <div className="flex items-center">
              <Move size={12} />
              <Apple size={12} color="lightgreen" />
            </div>
            <span className="text-xs">移动食物</span>
          </ToggleGroupItem>
        </ToggleGroup>
        
        {/* 当前工具说明 */}
        <div className="p-3 bg-green-900/20 rounded-lg border border-green-500/30">
          <p className="text-xs text-green-300">
            <strong>当前工具：</strong> {getToolDescription(activeTool)}
          </p>
        </div>
      </div>

      {/* 信息素行为说明 */}
      <div className="p-3 bg-blue-900/20 rounded-lg border border-blue-500/30">
        <h4 className="text-sm font-semibold text-blue-300 mb-2">信息素机制</h4>
        <div className="space-y-1 text-xs text-blue-200">
          <p><span className="inline-block w-3 h-3 bg-green-500 rounded mr-2"></span>绿色：觅食信息素（寻找食物时留下）</p>
          <p><span className="inline-block w-3 h-3 bg-blue-500 rounded mr-2"></span>蓝色：返程信息素（找到食物返回时留下）</p>
          <p><span className="inline-block w-3 h-3 bg-white rounded mr-2"></span>白点：正在寻找食物的蚂蚁</p>
          <p><span className="inline-block w-3 h-3 bg-yellow-400 rounded mr-2"></span>黄点：正在返回巢穴的蚂蚁</p>
        </div>
      </div>

      <Accordion type="multiple" className="w-full" defaultValue={["params"]}>
        <AccordionItem value="params">
          <AccordionTrigger className="text-green-300">模拟参数</AccordionTrigger>
          <AccordionContent className="space-y-4 pt-4">
            <div className="space-y-3">
              <Label className="text-green-200">蚂蚁数量: {params.antCount}</Label>
              <Slider 
                min={50} 
                max={300} 
                step={10} 
                value={[params.antCount]} 
                onValueChange={([v]) => handleParamChange('antCount', v)} 
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-green-500 [&>span:last-child]:border-green-500 [&>span:last-child]:bg-background" 
              />
            </div>
            <div className="space-y-3">
              <Label className="text-green-200">蒸发率: {params.evaporationRate.toFixed(3)}</Label>
              <Slider 
                min={0.95} 
                max={0.999} 
                step={0.001} 
                value={[params.evaporationRate]} 
                onValueChange={([v]) => handleParamChange('evaporationRate', v)} 
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-green-500 [&>span:last-child]:border-green-500 [&>span:last-child]:bg-background" 
              />
            </div>
            <div className="space-y-3">
              <Label className="text-green-200">信息素强度: {params.pheromoneStrength.toFixed(1)}</Label>
              <Slider 
                min={1} 
                max={15} 
                step={0.5} 
                value={[params.pheromoneStrength]} 
                onValueChange={([v]) => handleParamChange('pheromoneStrength', v)} 
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-green-500 [&>span:last-child]:border-green-500 [&>span:last-child]:bg-background" 
              />
            </div>
            <div className="space-y-3">
              <Label className="text-green-200">转向速度: {(params.turnSpeed * 180 / Math.PI).toFixed(1)}°</Label>
              <Slider 
                min={Math.PI / 32} 
                max={Math.PI / 8} 
                step={Math.PI / 64} 
                value={[params.turnSpeed]} 
                onValueChange={([v]) => handleParamChange('turnSpeed', v)} 
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-green-500 [&>span:last-child]:border-green-500 [&>span:last-child]:bg-background" 
              />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default AntColonyControlPanel;
