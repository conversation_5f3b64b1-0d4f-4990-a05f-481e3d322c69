import React, { useState, useEffect, useRef, useCallback } from 'react';
import { GPUAntColonySimulation, SimulationParams, Point } from '@/lib/physarum/gpu-core';
import AntColonyControlPanel, { Tool } from './PhysarumControlPanel';
import AntColonyRulesExplanation from './PhysarumRulesExplanation';

const initialParams: SimulationParams = {
  antCount: 150,
  evaporationRate: 0.98,
  pheromoneStrength: 8.0,
  trailStrength: 5.0,
  antSpeed: 1.2,
  sensorDistance: 9,
  sensorAngle: Math.PI / 6,
  turnSpeed: Math.PI / 20,
};

interface AntColonyPageProps {
  isActive: boolean;
}

const AntColonyPage: React.FC<AntColonyPageProps> = ({ isActive }) => {
  const [params, setParams] = useState<SimulationParams>(initialParams);
  const [isRunning, setIsRunning] = useState(true);
  const [activeTool, setActiveTool] = useState<Tool>('pointer');
  const [tick, setTick] = useState(0);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const simulationRef = useRef<GPUAntColonySimulation | null>(null);
  const animationFrameRef = useRef<number>(0);
  
  const isDraggingRef = useRef<{ type: Tool | 'none'; index?: number }>({ type: 'none' });

  const resetSimulation = useCallback(() => {
    if (!canvasRef.current) {
      console.warn('Canvas not available for reset');
      return;
    }
    
    // Dispose of old simulation
    if (simulationRef.current) {
      simulationRef.current.dispose();
      simulationRef.current = null;
    }
    
    try {
      const width = 800;
      const height = 600;
      canvasRef.current.width = width;
      canvasRef.current.height = height;
      
      const sim = new GPUAntColonySimulation(width, height, params, canvasRef.current);
      
      // Set nest and food positions BEFORE initializing
      sim.nestPosition = { x: width * 0.2, y: height * 0.8 };
      sim.foodSources = [
        { x: width * 0.8, y: height * 0.2 },
        { x: width * 0.7, y: height * 0.7 }
      ];
      
      console.log('Setting nest position:', sim.nestPosition);
      console.log('Setting food sources:', sim.foodSources);
      
      // Reset after setting positions
      sim.reset();
      simulationRef.current = sim;
      setTick(c => c + 1);
      
      console.log('Simulation reset complete');
    } catch (error) {
      console.error('Failed to initialize GPU simulation:', error);
    }
  }, [params]);

  // Initialize simulation on mount
  useEffect(() => {
    if (isActive) {
      console.log('Initializing simulation...');
      resetSimulation();
    }
    
    return () => {
      if (simulationRef.current) {
        simulationRef.current.dispose();
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [resetSimulation, isActive]);

  useEffect(() => {
    if (simulationRef.current) {
      simulationRef.current.setParams(params);
    }
  }, [params]);

  useEffect(() => {
    const loop = () => {
      if (isRunning && simulationRef.current && isActive) {
        simulationRef.current.update();
        setTick(c => c + 1);
      }
      if (isActive) {
        animationFrameRef.current = requestAnimationFrame(loop);
      }
    };
    
    if (isActive) {
      animationFrameRef.current = requestAnimationFrame(loop);
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }
    
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isRunning, isActive]);

  const handleCanvasInteraction = useCallback((e: React.MouseEvent) => {
    if (!simulationRef.current || !canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    
    const canvasX = e.clientX - rect.left;
    const canvasY = e.clientY - rect.top;
    const x = Math.floor((canvasX / rect.width) * simulationRef.current.width);
    const y = Math.floor((canvasY / rect.height) * simulationRef.current.height);
    
    const sim = simulationRef.current;
    const tool = isDraggingRef.current.type !== 'none' ? isDraggingRef.current.type : activeTool;

    switch (tool) {
      case 'draw':
      case 'erase':
        const isObstacle = tool === 'draw';
        for (let i = -3; i <= 3; i++) {
          for (let j = -3; j <= 3; j++) {
            if (i*i + j*j <= 9) {
              sim.setObstacle(x + i, y + j, isObstacle);
            }
          }
        }
        break;
      case 'move_nest':
        if(sim.nestPosition) {
          sim.nestPosition = { 
            x: Math.max(20, Math.min(sim.width-20, x)), 
            y: Math.max(20, Math.min(sim.height-20, y)) 
          };
          console.log('Moved nest to:', sim.nestPosition);
        }
        break;
      case 'move_food':
        const dragIndex = isDraggingRef.current.index ?? 0;
        if(sim.foodSources[dragIndex]) {
          sim.foodSources[dragIndex] = { 
            x: Math.max(20, Math.min(sim.width-20, x)), 
            y: Math.max(20, Math.min(sim.height-20, y)) 
          };
          console.log('Moved food source', dragIndex, 'to:', sim.foodSources[dragIndex]);
        }
        break;
    }
    setTick(t => t+1);
  }, [activeTool]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (activeTool === 'pointer') return;
    isDraggingRef.current.type = activeTool;
    
    if (activeTool === 'move_food') {
       const sim = simulationRef.current;
       if (!sim || !canvasRef.current) return;
       
       const canvas = canvasRef.current;
       const rect = canvas.getBoundingClientRect();
       const canvasX = e.clientX - rect.left;
       const canvasY = e.clientY - rect.top;
       const x = (canvasX / rect.width) * sim.width;
       const y = (canvasY / rect.height) * sim.height;

       let closestDist = Infinity;
       let closestIndex = -1;
       sim.foodSources.forEach((food, index) => {
           const dist = Math.hypot(food.x - x, food.y - y);
           if (dist < closestDist) {
               closestDist = dist;
               closestIndex = index;
           }
       });
       if (closestDist < 30) {
           isDraggingRef.current.index = closestIndex;
       } else {
           isDraggingRef.current.type = 'none';
       }
    }
    
    handleCanvasInteraction(e);
  };
  
  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDraggingRef.current.type === 'none') return;
    handleCanvasInteraction(e);
  };
  
  const handleMouseUp = () => {
    isDraggingRef.current = { type: 'none' };
  };

  return (
    <div className="flex flex-col md:flex-row h-[calc(100vh-150px)] w-full bg-background">
      <div 
        ref={containerRef}
        className="flex-grow h-full w-full md:w-auto relative border-2 border-green-500/20 rounded-lg overflow-hidden"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <canvas
          ref={canvasRef}
          className="w-full h-full bg-black"
          style={{ imageRendering: 'pixelated' }}
        />
        <AntColonyRulesExplanation />
      </div>
      <AntColonyControlPanel
        params={params}
        setParams={setParams}
        isRunning={isRunning}
        setIsRunning={setIsRunning}
        resetSimulation={resetSimulation}
        activeTool={activeTool}
        setActiveTool={setActiveTool}
      />
    </div>
  );
};

export default AntColonyPage;
